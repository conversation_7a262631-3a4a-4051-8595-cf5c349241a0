package com.gw.agent.controller;

import com.github.pagehelper.PageInfo;
import com.gw.common.agent.dto.SensitiveWordCheckDTO;
import com.gw.agent.dto.SensitiveWordExportDTO;
import com.gw.agent.dto.SensitiveWordQueryDTO;
import com.gw.agent.dto.SensitiveWordStatusUpdateDTO;
import com.gw.agent.dto.SensitiveWordSubmitDTO;
import com.gw.agent.dto.SensitiveWordUpdateDTO;
import com.gw.agent.entity.SensitiveWordEntity;
import com.gw.agent.service.SensitiveWordDetectionService;
import com.gw.agent.service.SensitiveWordService;
import com.gw.common.agent.dto.SensitiveWordCheckResultVO;
import com.gw.agent.vo.SensitiveWordImportResultVO;
import com.gw.agent.vo.SensitiveWordVO;
import com.gw.common.dto.ItemIdDTO;

import com.gw.common.dto.PageBaseRequest;
import com.gw.common.dto.ResponseResult;

import com.gw.common.user.context.UserContextUtil;
import com.gw.common.vo.PageBaseContentVo;
import com.gw.common.vo.PaginationVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

import static com.gw.common.exception.BusinessExceptionCode.FAIL_CODE;


/**
 * 敏感词控制器
 */
@RestController
@RequestMapping("/api/v1/sensitive-word")
@RequiredArgsConstructor
@Tag(name = "敏感词管理", description = "敏感词相关API")
@Log4j2
public class SensitiveWordController {
    
    private final SensitiveWordService sensitiveWordService;
    private final SensitiveWordDetectionService detectionService;
    
    /**
     * 创建敏感词
     */
    @Operation(summary = "创建敏感词", description = "创建一个新的敏感词")
    @PostMapping("")
    public ResponseResult<?> createSensitiveWord(@RequestBody @Valid SensitiveWordSubmitDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        
        SensitiveWordEntity entity = new SensitiveWordEntity();
        BeanUtils.copyProperties(req, entity);
        entity.setCreator(username);
        entity.setUpdater(username);
        
        sensitiveWordService.insert(entity);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 更新敏感词
     */
    @Operation(summary = "更新敏感词", description = "更新敏感词信息")
    @PostMapping("/update")
    public ResponseResult<?> updateSensitiveWord(@RequestBody @Valid SensitiveWordUpdateDTO req) {
        String username = UserContextUtil.getCurrentUsername();
        
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        // 只更新非空字段
        if (req.getWord() != null) entity.setWord(req.getWord());
        if (req.getCategoryId() != null) entity.setCategoryId(req.getCategoryId());
        if (req.getLevel() != null) entity.setLevel(req.getLevel());
        if (req.getAction() != null) entity.setAction(req.getAction());
        if (req.getReplacement() != null) entity.setReplacement(req.getReplacement());
        if (req.getStatus() != null) entity.setStatus(req.getStatus());

        entity.setUpdater(username);
        entity.setUpdateTime(LocalDateTime.now());
        
        sensitiveWordService.update(entity);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 删除敏感词
     */
    @Operation(summary = "删除敏感词", description = "删除指定的敏感词")
    @PostMapping("/delete")
    public ResponseResult<?> deleteSensitiveWord(@RequestBody @Valid ItemIdDTO req) {
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }

        sensitiveWordService.delete(req.getId());

        // 刷新检测服务缓存
        detectionService.refreshCache();

        return ResponseResult.success(null);
    }
    
    /**
     * 获取敏感词详情
     */
    @Operation(summary = "获取敏感词详情", description = "获取敏感词详细信息")
    @PostMapping("/get")
    public ResponseResult<SensitiveWordVO> getSensitiveWord(@RequestBody @Valid ItemIdDTO req) {
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }
        
        return ResponseResult.success(new SensitiveWordVO(entity));
    }
    
    /**
     * 分页查询敏感词
     */
    @Operation(summary = "分页查询敏感词", description = "分页获取敏感词列表")
    @PostMapping("/page")
    public ResponseResult<PageBaseContentVo<SensitiveWordVO>> pageSensitiveWords(
            @RequestBody @Valid PageBaseRequest<SensitiveWordQueryDTO> params) {
        
        if (params.getFilter() == null) {
            params.setFilter(new SensitiveWordQueryDTO());
        }
        
        PageInfo<SensitiveWordEntity> pageInfo = sensitiveWordService.page(
                params.getCurrent(), params.getPageSize(), params.getFilter());
        
        List<SensitiveWordVO> vos = pageInfo.getList().stream()
            .map(SensitiveWordVO::new)
            .collect(Collectors.toList());
        
        PageBaseContentVo<SensitiveWordVO> result = new PageBaseContentVo<>();
        PaginationVo pagination = new PaginationVo(
                pageInfo.getTotal(),
                pageInfo.getPageNum(),
                pageInfo.getPageSize());
        result.setList(vos);
        result.setPagination(pagination);
        
        return ResponseResult.success(result);
    }
    
    /**
     * 启用/禁用敏感词
     */
    @Operation(summary = "启用/禁用敏感词", description = "更新敏感词的启用状态")
    @PostMapping("/status/update")
    public ResponseResult<?> updateSensitiveWordStatus(@RequestBody @Valid SensitiveWordStatusUpdateDTO req) {
        SensitiveWordEntity entity = sensitiveWordService.findById(req.getId());
        if (entity == null) {
            return ResponseResult.failure(FAIL_CODE.getCode(), "敏感词不存在");
        }

        sensitiveWordService.updateStatus(req.getId(), req.getStatus());

        // 刷新检测服务缓存
        detectionService.refreshCache();

        return ResponseResult.success(null);
    }
    
    /**
     * 批量删除敏感词
     */
    @Operation(summary = "批量删除敏感词", description = "批量删除指定的敏感词")
    @PostMapping("/batch/delete")
    public ResponseResult<?> batchDeleteSensitiveWords(@RequestBody List<Long> ids) {
        sensitiveWordService.batchDelete(ids);
        
        // 刷新检测服务缓存
        detectionService.refreshCache();
        
        return ResponseResult.success(null);
    }
    
    /**
     * 检测文本中的敏感词
     */
    @Operation(summary = "检测文本敏感词", description = "检测文本中是否包含敏感词")
    @PostMapping("/check")
    public ResponseResult<SensitiveWordCheckResultVO> checkText(@RequestBody @Valid SensitiveWordCheckDTO req) {
        SensitiveWordCheckResultVO result = detectionService.checkText(req.getContent(), req.getDetailed());

        
        return ResponseResult.success(result);
    }

    
    /**
     * 刷新敏感词库缓存
     */
    @Operation(summary = "刷新敏感词库缓存", description = "刷新敏感词检测服务的缓存")
    @PostMapping("/cache/refresh")
    public ResponseResult<?> refreshCache() {
        detectionService.refreshCache();
        return ResponseResult.success(null);
    }
    
    /**
     * 清除敏感词缓存
     */
    @Operation(summary = "清除敏感词缓存", description = "清除敏感词相关缓存")
    @PostMapping("/cache/clear")
    public ResponseResult<?> clearCache() {
        sensitiveWordService.clearCache();
        return ResponseResult.success(null);
    }

    /**
     * 导入敏感词文件
     */
    @Operation(summary = "导入敏感词文件", description = "从文件导入敏感词，支持txt格式，一行一个敏感词")
    @PostMapping("/import")
    public ResponseResult<SensitiveWordImportResultVO> importSensitiveWords(
            @RequestParam("file") MultipartFile file,
            @RequestParam("categoryId") Long categoryId,
            @RequestParam(value = "level", defaultValue = "1") Integer level,
            @RequestParam(value = "action", defaultValue = "1") Integer action,
            @RequestParam(value = "replacement", required = false) String replacement,
            @RequestParam(value = "status", defaultValue = "1") Integer status,
            @RequestParam(value = "overwrite", defaultValue = "false") Boolean overwrite) {

        SensitiveWordImportResultVO result = sensitiveWordService.importFromFile(
                file, categoryId, level, action, replacement, status, overwrite);

        // 刷新检测服务缓存
        detectionService.refreshCache();

        return ResponseResult.success(result);
    }

    /**
     * 导出敏感词文件
     */
    @Operation(summary = "导出敏感词文件", description = "导出敏感词到txt文件")
    @PostMapping("/export")
    public ResponseEntity<byte[]> exportSensitiveWords(@RequestBody SensitiveWordExportDTO exportDTO) {

        byte[] fileContent = sensitiveWordService.exportToFile(exportDTO);

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = exportDTO.getFileNamePrefix() + "_" + timestamp + ".txt";

        try {
            fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            log.warn("文件名编码失败: {}", e.getMessage());
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", fileName);
        headers.setContentLength(fileContent.length);

        return ResponseEntity.ok()
                .headers(headers)
                .body(fileContent);
    }
}
