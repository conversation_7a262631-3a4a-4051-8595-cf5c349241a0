package com.gw.agent.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 敏感词检测参数
 */
@Data
@Schema(description = "敏感词检测参数")
public class SensitiveWordCheckDTO {
    
    @Schema(description = "待检测文本内容")
    @NotBlank(message = "待检测文本内容不能为空")
    private String content;
    
    @Schema(description = "是否返回详细信息")
    @JsonIgnore
    private Boolean detailed = false;
    
    @Schema(description = "是否自动替换敏感词")
    @JsonIgnore
    private Boolean autoReplace = false;
    
    @Schema(description = "自定义替换字符")
    @JsonIgnore
    private String replaceChar = "*";
}
