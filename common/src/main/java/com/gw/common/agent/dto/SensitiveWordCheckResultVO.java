package com.gw.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 敏感词检测结果视图对象
 */
@Data
@Schema(description = "敏感词检测结果VO")
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordCheckResultVO {
    
    @Schema(description = "是否包含敏感词")
    private Boolean hasSensitiveWord;
    
    @Schema(description = "检测到的敏感词列表")
    private List<String> sensitiveWords;
    
    @Schema(description = "原始文本")
    private String originalText;
    
    @Schema(description = "处理后的文本（如果启用了自动替换）")
    private String processedText;
    
    @Schema(description = "敏感词详细信息")
    private List<SensitiveWordDetailVO> details;
    
    /**
     * 敏感词详细信息
     */
    @Data
    @Schema(description = "敏感词详细信息")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SensitiveWordDetailVO {
        
        @Schema(description = "敏感词")
        private String word;
        
        @Schema(description = "在文本中的位置")
        private Integer position;
        
        @Schema(description = "敏感词级别")
        private Integer level;
        
        @Schema(description = "处理动作")
        private Integer action;
        
        @Schema(description = "替换内容")
        private String replacement;
        
        @Schema(description = "分类名称")
        private String categoryName;
    }
}
